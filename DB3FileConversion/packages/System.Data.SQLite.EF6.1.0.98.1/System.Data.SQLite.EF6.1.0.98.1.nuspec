<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>System.Data.SQLite.EF6</id>
    <version>********</version>
    <title>System.Data.SQLite EF6</title>
    <authors>SQLite Development Team</authors>
    <owners>SQLite Development Team</owners>
    <licenseUrl>https://www.sqlite.org/copyright.html</licenseUrl>
    <projectUrl>https://system.data.sqlite.org/</projectUrl>
    <iconUrl>https://system.data.sqlite.org/images/sqlite128.png</iconUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>Support for Entity Framework 6 using System.Data.SQLite.</description>
    <copyright>Public Domain</copyright>
    <language>en-US</language>
    <tags>sqlite database ado.net provider interop</tags>
    <dependencies>
      <group targetFramework=".NETFramework4.0">
        <dependency id="EntityFramework" version="*******" />
      </group>
      <group targetFramework=".NETFramework4.5">
        <dependency id="EntityFramework" version="*******" />
      </group>
      <group targetFramework=".NETFramework4.5.1">
        <dependency id="EntityFramework" version="*******" />
      </group>
      <group targetFramework=".NETFramework4.6">
        <dependency id="EntityFramework" version="*******" />
      </group>
    </dependencies>
  </metadata>
</package>